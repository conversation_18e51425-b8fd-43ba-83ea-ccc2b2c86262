<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鸣潮角色强度排行榜 | Wuthering Waves Character Tier List</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #e2e8f0;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            min-height: 100vh;
            padding: 2rem 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .title {
            font-size: 3rem;
            font-weight: bold;
            color: #e2e8f0;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.125rem;
            color: #94a3b8;
            margin-bottom: 1.5rem;
        }
        
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 215, 0, 0.5);
        }
        
        .button {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            border: none;
            cursor: pointer;
            margin: 0.25rem;
            transition: all 0.2s ease;
        }
        
        .button-primary {
            background: #ffd700;
            color: #1a1a2e;
        }
        
        .button-secondary {
            background: #16213e;
            color: #e2e8f0;
        }
        
        .button-secondary:hover {
            background: #0f3460;
            color: #ffd700;
        }
        
        .progress-bar {
            width: 100%;
            background: #334155;
            border-radius: 9999px;
            height: 0.5rem;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 9999px;
            transition: width 0.3s ease;
        }
        
        .tier-s-plus {
            color: #f87171;
            background: rgba(239, 68, 68, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .tier-s {
            color: #fb923c;
            background: rgba(249, 115, 22, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .tier-a {
            color: #4ade80;
            background: rgba(34, 197, 94, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .tier-b-plus {
            color: #60a5fa;
            background: rgba(59, 130, 246, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .character-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .character-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .rank {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .character-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .character-details {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .element-anemo { color: #4ecdc4; }
        .element-glacio { color: #87ceeb; }
        .element-fusion { color: #ff6b6b; }
        .element-electro { color: #9b59b6; }
        .element-havoc { color: #2c3e50; }
        .element-spectro { color: #f39c12; }
        
        .stars {
            color: #ffd700;
        }
        
        .description {
            color: #94a3b8;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .stats-section {
            margin-bottom: 1rem;
        }
        
        .stat-item {
            margin-bottom: 0.5rem;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: #94a3b8;
        }
        
        .stat-value {
            font-weight: 500;
        }
        
        .pros-cons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            font-size: 0.875rem;
        }
        
        .pros h4 {
            color: #4ade80;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .cons h4 {
            color: #f87171;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .pros ul, .cons ul {
            list-style: none;
            padding: 0;
        }
        
        .pros li, .cons li {
            color: #94a3b8;
            margin-bottom: 0.25rem;
        }
        
        .footer {
            margin-top: 3rem;
            text-align: center;
            color: #94a3b8;
        }
        
        .sort-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .character-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .character-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1 class="title">鸣潮角色强度排行榜</h1>
            <p class="subtitle">基于当前版本的角色强度分析与排名</p>
            <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
                <button class="button button-primary" onclick="shareRanking()">📤 分享排行榜</button>
                <div class="card" style="padding: 0.75rem 1.5rem; margin: 0;">
                    <span style="color: #94a3b8;">更新时间: 2024/12/10</span>
                </div>
            </div>
        </header>

        <!-- 排序控制 -->
        <div class="card">
            <h3 style="font-size: 1.125rem; font-weight: bold; margin-bottom: 1rem;">排序方式</h3>
            <div class="sort-controls">
                <button class="button button-primary">综合评分 ↓</button>
                <button class="button button-secondary">DPS输出</button>
                <button class="button button-secondary">生存能力</button>
                <button class="button button-secondary">辅助能力</button>
                <button class="button button-secondary">实用性</button>
                <button class="button button-secondary">角色名称</button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="card" style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; color: #ffd700;">9</div>
                <div style="color: #94a3b8;">总角色数</div>
            </div>
            <div class="card" style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; color: #f87171;">3</div>
                <div style="color: #94a3b8;">S+级角色</div>
            </div>
            <div class="card" style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; color: #ffd700;">5</div>
                <div style="color: #94a3b8;">五星角色</div>
            </div>
            <div class="card" style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; color: #60a5fa;">80</div>
                <div style="color: #94a3b8;">平均评分</div>
            </div>
        </div>

        <!-- 角色列表 -->
        <div style="display: flex; flex-direction: column; gap: 1.5rem;">
            <!-- 今汐 -->
            <div class="card">
                <div class="character-header">
                    <div class="character-info">
                        <div class="rank">🥇</div>
                        <div>
                            <div class="character-name">今汐</div>
                            <div class="character-details">
                                <span class="element-spectro">Spectro</span>
                                <span style="color: #94a3b8;">•</span>
                                <span style="color: #94a3b8;">Sword</span>
                                <span style="color: #94a3b8;">•</span>
                                <span class="stars">★★★★★</span>
                            </div>
                        </div>
                    </div>
                    <div class="tier-s-plus">S+</div>
                </div>
                <p class="description">光谱属性主C，拥有极强的输出能力和优秀的机动性</p>
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">综合评分</span>
                            <span class="stat-value">91</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 91%; background: #ffd700;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">DPS输出</span>
                            <span class="stat-value">95</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 95%; background: #ef4444;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">生存能力</span>
                            <span class="stat-value">75</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%; background: #22c55e;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">辅助能力</span>
                            <span class="stat-value">85</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%; background: #3b82f6;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">实用性</span>
                            <span class="stat-value">90</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%; background: #a855f7;"></div>
                        </div>
                    </div>
                </div>
                <div class="pros-cons">
                    <div class="pros">
                        <h4>优势</h4>
                        <ul>
                            <li>• 超高单体输出</li>
                            <li>• 优秀的机动性</li>
                            <li>• 强力的共鸣技能</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <h4>劣势</h4>
                        <ul>
                            <li>• 对操作要求较高</li>
                            <li>• 需要特定队伍配置</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 维里奈 -->
            <div class="card">
                <div class="character-header">
                    <div class="character-info">
                        <div class="rank">🥈</div>
                        <div>
                            <div class="character-name">维里奈</div>
                            <div class="character-details">
                                <span class="element-spectro">Spectro</span>
                                <span style="color: #94a3b8;">•</span>
                                <span style="color: #94a3b8;">Rectifier</span>
                                <span style="color: #94a3b8;">•</span>
                                <span class="stars">★★★★★</span>
                            </div>
                        </div>
                    </div>
                    <div class="tier-s-plus">S+</div>
                </div>
                <p class="description">光谱属性辅助角色，顶级的治疗和增益能力</p>
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">综合评分</span>
                            <span class="stat-value">89</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 89%; background: #ffd700;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">DPS输出</span>
                            <span class="stat-value">70</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 70%; background: #ef4444;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">生存能力</span>
                            <span class="stat-value">90</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%; background: #22c55e;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">辅助能力</span>
                            <span class="stat-value">95</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 95%; background: #3b82f6;"></div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-header">
                            <span class="stat-label">实用性</span>
                            <span class="stat-value">92</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 92%; background: #a855f7;"></div>
                        </div>
                    </div>
                </div>
                <div class="pros-cons">
                    <div class="pros">
                        <h4>优势</h4>
                        <ul>
                            <li>• 顶级治疗能力</li>
                            <li>• 强力团队增益</li>
                            <li>• 优秀的生存保障</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <h4>劣势</h4>
                        <ul>
                            <li>• 个人输出较低</li>
                            <li>• 依赖队友配合</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="footer">
            <p style="margin-bottom: 0.5rem;">数据仅供参考，实际游戏体验可能因个人操作和队伍配置而异</p>
            <p style="font-size: 0.875rem;">© 2024 鸣潮角色强度排行榜 - 非官方数据分析</p>
        </footer>
    </div>

    <script>
        function shareRanking() {
            const url = window.location.href;
            if (navigator.share) {
                navigator.share({
                    title: '鸣潮角色强度排行榜',
                    text: '查看最新的鸣潮角色强度排行榜，了解各角色的详细数据分析！',
                    url: url,
                }).catch(console.error);
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板！');
                }).catch(() => {
                    alert('复制失败，请手动复制链接');
                });
            }
        }
    </script>
</body>
</html>
