(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},1169:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>g,tree:()=>d});var s=r(482),i=r(9108),a=r(2563),o=r.n(a),n=r(8300),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1136)),"C:\\Users\\<USER>\\Desktop\\mingchao\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1342)),"C:\\Users\\<USER>\\Desktop\\mingchao\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\mingchao\\src\\app\\page.tsx"],p="/page",m={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1e3:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},8152:()=>{},9768:(e,t,r)=>{Promise.resolve().then(r.bind(r,9822))},2295:(e,t,r)=>{"use strict";e.exports=r(6372).vendored["react-ssr"].ReactJsxRuntime},9822:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(2295),i=r(3729);let a=[{id:"jinxi",name:"今汐",element:"Spectro",weapon:"Sword",rarity:5,stats:{dps:95,survival:75,support:85,utility:90,overall:91},tier:"S+",description:"光谱属性主C，拥有极强的输出能力和优秀的机动性",strengths:["超高单体输出","优秀的机动性","强力的共鸣技能"],weaknesses:["对操作要求较高","需要特定队伍配置"]},{id:"changli",name:"长离",element:"Fusion",weapon:"Sword",rarity:5,stats:{dps:92,survival:70,support:60,utility:85,overall:88},tier:"S+",description:"热熔属性主C，拥有强大的爆发输出和范围伤害",strengths:["极高爆发伤害","优秀的范围清怪","华丽的技能特效"],weaknesses:["技能CD较长","对能量管理要求高"]},{id:"chixia",name:"炽霞",element:"Fusion",weapon:"Pistols",rarity:4,stats:{dps:78,survival:65,support:45,utility:70,overall:72},tier:"A",description:"热熔属性输出角色，适合新手使用的强力四星",strengths:["操作简单","输出稳定","获取容易"],weaknesses:["天花板较低","后期乏力"]},{id:"kakaluo",name:"卡卡罗",element:"Electro",weapon:"Broadblade",rarity:5,stats:{dps:88,survival:85,support:70,utility:82,overall:85},tier:"S",description:"雷属性主C，平衡的攻防能力和稳定的输出",strengths:["攻防平衡","稳定输出","适应性强"],weaknesses:["缺乏爆发性","特色不够突出"]},{id:"yinlin",name:"吟霖",element:"Electro",weapon:"Rectifier",rarity:5,stats:{dps:90,survival:60,support:75,utility:88,overall:86},tier:"S",description:"雷属性输出角色，拥有独特的连携机制",strengths:["独特连携机制","高机动性","优秀的单体输出"],weaknesses:["生存能力较弱","需要熟练操作"]},{id:"weilinnai",name:"维里奈",element:"Spectro",weapon:"Rectifier",rarity:5,stats:{dps:70,survival:90,support:95,utility:92,overall:89},tier:"S+",description:"光谱属性辅助角色，顶级的治疗和增益能力",strengths:["顶级治疗能力","强力团队增益","优秀的生存保障"],weaknesses:["个人输出较低","依赖队友配合"]},{id:"baizhi",name:"白芷",element:"Glacio",weapon:"Rectifier",rarity:4,stats:{dps:45,survival:85,support:88,utility:75,overall:73},tier:"A",description:"冰属性治疗角色，稳定的团队支援能力",strengths:["稳定治疗","冰属性共鸣","获取容易"],weaknesses:["输出能力极低","技能单一"]},{id:"yangyang",name:"秧秧",element:"Anemo",weapon:"Sword",rarity:4,stats:{dps:65,survival:70,support:60,utility:68,overall:66},tier:"B+",description:"风属性辅助角色，提供团队增益和控制",strengths:["风属性聚怪","团队增益","操作简单"],weaknesses:["输出偏低","后期作用有限"]},{id:"sanhua",name:"散华",element:"Glacio",weapon:"Sword",rarity:4,stats:{dps:75,survival:65,support:55,utility:70,overall:69},tier:"B+",description:"冰属性副C角色，提供冰属性伤害和控制",strengths:["冰属性输出","控制能力","配队灵活"],weaknesses:["天花板较低","需要特定配队"]}],o=[{key:"overall",label:"综合评分",order:"desc"},{key:"dps",label:"DPS输出",order:"desc"},{key:"survival",label:"生存能力",order:"desc"},{key:"support",label:"辅助能力",order:"desc"},{key:"utility",label:"实用性",order:"desc"},{key:"name",label:"角色名称",order:"asc"}];function n(){let[e,t]=(0,i.useState)(o[0]),r=(0,i.useMemo)(()=>[...a].sort((t,r)=>{let s=t.stats[e.key]||t[e.key],i=r.stats[e.key]||r[e.key];return"string"==typeof s&&"string"==typeof i?"asc"===e.order?s.localeCompare(i):i.localeCompare(s):"number"==typeof s&&"number"==typeof i?"asc"===e.order?s-i:i-s:0}),[e]),n={container:{minHeight:"100vh",background:"linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)",color:"#e2e8f0",fontFamily:"'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif",padding:"2rem 1rem"},header:{textAlign:"center",marginBottom:"2rem"},title:{fontSize:"3rem",fontWeight:"bold",color:"#e2e8f0",marginBottom:"1rem"},subtitle:{fontSize:"1.125rem",color:"#94a3b8",marginBottom:"1.5rem"},card:{background:"#1e293b",border:"1px solid #334155",borderRadius:"0.5rem",padding:"1.5rem",marginBottom:"1.5rem",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.3)",transition:"all 0.3s ease"},button:{padding:"0.75rem 1.5rem",borderRadius:"0.5rem",fontWeight:"600",border:"none",cursor:"pointer",margin:"0.25rem",transition:"all 0.2s ease"},primaryButton:{background:"#ffd700",color:"#1a1a2e"},secondaryButton:{background:"#16213e",color:"#e2e8f0"},progressBar:{width:"100%",background:"#334155",borderRadius:"9999px",height:"0.5rem",overflow:"hidden",marginBottom:"0.5rem"},progressFill:{height:"100%",borderRadius:"9999px",transition:"width 0.3s ease"}},l=async()=>{let e=window.location.href;if(navigator.share)try{await navigator.share({title:"鸣潮角色强度排行榜",text:"查看最新的鸣潮角色强度排行榜，了解各角色的详细数据分析！",url:e})}catch(t){console.log("分享失败:",t),d(e)}else d(e)},d=e=>{navigator.clipboard.writeText(e).then(()=>{alert("链接已复制到剪贴板！")}).catch(()=>{alert("复制失败，请手动复制链接")})};return s.jsx("div",{style:n.container,children:(0,s.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[(0,s.jsxs)("header",{style:n.header,children:[s.jsx("h1",{style:n.title,children:"鸣潮角色强度排行榜"}),s.jsx("p",{style:n.subtitle,children:"基于当前版本的角色强度分析与排名"}),(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"center",gap:"1rem",flexWrap:"wrap"},children:[s.jsx("button",{onClick:l,style:{...n.button,...n.primaryButton},children:"\uD83D\uDCE4 分享排行榜"}),s.jsx("div",{style:{...n.card,padding:"0.75rem 1.5rem",margin:0},children:(0,s.jsxs)("span",{style:{color:"#94a3b8"},children:["更新时间: ",new Date().toLocaleDateString("zh-CN")]})})]})]}),(0,s.jsxs)("div",{style:n.card,children:[s.jsx("h3",{style:{fontSize:"1.125rem",fontWeight:"bold",marginBottom:"1rem"},children:"排序方式"}),s.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"0.5rem"},children:o.map(r=>(0,s.jsxs)("button",{onClick:()=>t(r),style:{...n.button,...e.key===r.key?n.primaryButton:n.secondaryButton},children:[r.label,e.key===r.key&&s.jsx("span",{style:{marginLeft:"0.5rem"},children:"desc"===r.order?"↓":"↑"})]},r.key))})]}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"1rem",marginBottom:"2rem"},children:[(0,s.jsxs)("div",{style:{...n.card,textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"2rem",fontWeight:"bold",color:"#ffd700"},children:a.length}),s.jsx("div",{style:{color:"#94a3b8"},children:"总角色数"})]}),(0,s.jsxs)("div",{style:{...n.card,textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"2rem",fontWeight:"bold",color:"#f87171"},children:a.filter(e=>"S+"===e.tier).length}),s.jsx("div",{style:{color:"#94a3b8"},children:"S+级角色"})]}),(0,s.jsxs)("div",{style:{...n.card,textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"2rem",fontWeight:"bold",color:"#ffd700"},children:a.filter(e=>5===e.rarity).length}),s.jsx("div",{style:{color:"#94a3b8"},children:"五星角色"})]}),(0,s.jsxs)("div",{style:{...n.card,textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"2rem",fontWeight:"bold",color:"#60a5fa"},children:Math.round(a.reduce((e,t)=>e+t.stats.overall,0)/a.length)}),s.jsx("div",{style:{color:"#94a3b8"},children:"平均评分"})]})]}),s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"1.5rem"},children:r.map((e,t)=>{var r;let i={"S+":{color:"#f87171",background:"rgba(239, 68, 68, 0.2)"},S:{color:"#fb923c",background:"rgba(249, 115, 22, 0.2)"},"A+":{color:"#fbbf24",background:"rgba(245, 158, 11, 0.2)"},A:{color:"#4ade80",background:"rgba(34, 197, 94, 0.2)"},"B+":{color:"#60a5fa",background:"rgba(59, 130, 246, 0.2)"},B:{color:"#a78bfa",background:"rgba(139, 92, 246, 0.2)"},C:{color:"#9ca3af",background:"rgba(107, 114, 128, 0.2)"}};return(0,s.jsxs)("div",{style:n.card,children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"1rem"},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:[s.jsx("div",{style:{fontSize:"2rem",fontWeight:"bold"},children:1===(r=t+1)?"\uD83E\uDD47":2===r?"\uD83E\uDD48":3===r?"\uD83E\uDD49":`#${r}`}),(0,s.jsxs)("div",{children:[s.jsx("h3",{style:{fontSize:"1.5rem",fontWeight:"bold",marginBottom:"0.5rem"},children:e.name}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem",fontSize:"0.875rem"},children:[s.jsx("span",{style:{color:{Anemo:"#4ecdc4",Glacio:"#87ceeb",Fusion:"#ff6b6b",Electro:"#9b59b6",Havoc:"#2c3e50",Spectro:"#f39c12"}[e.element],fontWeight:"500"},children:e.element}),s.jsx("span",{style:{color:"#94a3b8"},children:"•"}),s.jsx("span",{style:{color:"#94a3b8"},children:e.weapon}),s.jsx("span",{style:{color:"#94a3b8"},children:"•"}),s.jsx("div",{style:{display:"flex"},children:Array.from({length:e.rarity},(e,t)=>s.jsx("span",{style:{color:"#ffd700"},children:"★"},t))})]})]})]}),s.jsx("div",{style:{padding:"0.5rem 1rem",borderRadius:"9999px",fontSize:"0.875rem",fontWeight:"bold",color:i[e.tier].color,background:i[e.tier].background},children:e.tier})]}),s.jsx("p",{style:{color:"#94a3b8",fontSize:"0.875rem",marginBottom:"1rem",lineHeight:"1.5"},children:e.description}),s.jsx("div",{style:{marginBottom:"1rem"},children:[{label:"综合评分",value:e.stats.overall,color:"#ffd700"},{label:"DPS输出",value:e.stats.dps,color:"#ef4444"},{label:"生存能力",value:e.stats.survival,color:"#22c55e"},{label:"辅助能力",value:e.stats.support,color:"#3b82f6"},{label:"实用性",value:e.stats.utility,color:"#a855f7"}].map(e=>(0,s.jsxs)("div",{style:{marginBottom:"0.5rem"},children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"0.875rem",marginBottom:"0.25rem"},children:[s.jsx("span",{style:{color:"#94a3b8"},children:e.label}),s.jsx("span",{style:{fontWeight:"500"},children:e.value})]}),s.jsx("div",{style:n.progressBar,children:s.jsx("div",{style:{...n.progressFill,width:`${e.value}%`,background:e.color}})})]},e.label))}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"1rem",fontSize:"0.875rem"},children:[(0,s.jsxs)("div",{children:[s.jsx("h4",{style:{color:"#4ade80",fontWeight:"500",marginBottom:"0.5rem"},children:"优势"}),s.jsx("ul",{style:{listStyle:"none",padding:0},children:e.strengths.map((e,t)=>(0,s.jsxs)("li",{style:{color:"#94a3b8",marginBottom:"0.25rem"},children:["• ",e]},t))})]}),(0,s.jsxs)("div",{children:[s.jsx("h4",{style:{color:"#f87171",fontWeight:"500",marginBottom:"0.5rem"},children:"劣势"}),s.jsx("ul",{style:{listStyle:"none",padding:0},children:e.weaknesses.map((e,t)=>(0,s.jsxs)("li",{style:{color:"#94a3b8",marginBottom:"0.25rem"},children:["• ",e]},t))})]})]})]},e.id)})}),(0,s.jsxs)("footer",{style:{marginTop:"3rem",textAlign:"center",color:"#94a3b8"},children:[s.jsx("p",{style:{marginBottom:"0.5rem"},children:"数据仅供参考，实际游戏体验可能因个人操作和队伍配置而异"}),s.jsx("p",{style:{fontSize:"0.875rem"},children:"\xa9 2024 鸣潮角色强度排行榜 - 非官方数据分析"})]})]})})}},1342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var s=r(5036);r(5023);let i={title:"鸣潮角色强度排行榜 | Wuthering Waves Character Tier List",description:"最新的鸣潮角色强度排行榜，包含详细的角色数据分析、强度评分和排名。了解每个角色的DPS输出、生存能力、辅助能力等综合数据。",keywords:"鸣潮,角色排行榜,强度榜,tier list,wuthering waves,今汐,长离,炽霞,卡卡罗,吟霖,维里奈",authors:[{name:"鸣潮数据分析"}],creator:"鸣潮角色强度排行榜",publisher:"鸣潮角色强度排行榜",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://wuthering-waves-tier-list.vercel.app"),alternates:{canonical:"/"},openGraph:{title:"鸣潮角色强度排行榜",description:"最新的鸣潮角色强度排行榜，包含详细的角色数据分析和排名",url:"https://wuthering-waves-tier-list.vercel.app",siteName:"鸣潮角色强度排行榜",locale:"zh_CN",type:"website",images:[{url:"/og-image.png",width:1200,height:630,alt:"鸣潮角色强度排行榜"}]},twitter:{card:"summary_large_image",title:"鸣潮角色强度排行榜",description:"最新的鸣潮角色强度排行榜，包含详细的角色数据分析和排名",images:["/og-image.png"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function a({children:e}){return(0,s.jsxs)("html",{lang:"zh-CN",children:[(0,s.jsxs)("head",{children:[s.jsx("link",{rel:"icon",href:"/favicon.ico"}),s.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),s.jsx("meta",{name:"theme-color",content:"#1a1a2e"}),s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1"})]}),s.jsx("body",{className:"antialiased",children:e})]})}},1136:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>o});let s=(0,r(6843).createProxy)(String.raw`C:\Users\<USER>\Desktop\mingchao\src\app\page.tsx`),{__esModule:i,$$typeof:a}=s,o=s.default},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[73],()=>r(1169));module.exports=s})();