{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "460c0950dfe81534b16f880eba870696", "previewModeSigningKey": "f3695b0c92eadf1fca3a86668d4700db9192ed661ef9b631cba5fa44dc3d727c", "previewModeEncryptionKey": "89328179cbea20a3bf0f977bf76b59f70e500fc2ef6be0fb8a756eb75c59981b"}}