{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "f31446262b173b342e7e1db178cf8497", "previewModeSigningKey": "75928236fae98469f2cc01cc0fb272208394644da552a3e7143c9a674bbd2907", "previewModeEncryptionKey": "80da93826b297c29bd5b0278db9f461d7f344e6015df10a3fc71673dfc8e5c2c"}}