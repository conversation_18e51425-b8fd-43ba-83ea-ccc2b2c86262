# 鸣潮角色强度排行榜 - 部署指南

## 快速部署到免费平台

### 1. Vercel 部署（推荐）

Vercel 是最简单的部署方式，支持自动部署和免费域名。

**步骤：**

1. 将代码推送到 GitHub 仓库
2. 访问 [vercel.com](https://vercel.com) 并用 GitHub 账号登录
3. 点击 "New Project" 导入你的 GitHub 仓库
4. Vercel 会自动检测到这是 Next.js 项目并配置构建设置
5. 点击 "Deploy" 开始部署
6. 几分钟后，你会得到一个 `.vercel.app` 域名的网站

**自定义域名：**
- 在 Vercel 项目设置中可以添加自定义域名
- 支持免费的 SSL 证书

### 2. Netlify 部署

**步骤：**

1. 运行构建命令：
   ```bash
   npm run build
   ```

2. 将生成的 `out` 文件夹上传到 [netlify.com](https://netlify.com)
3. 或者连接 GitHub 仓库进行自动部署

**构建设置：**
- Build command: `npm run build`
- Publish directory: `out`

### 3. GitHub Pages 部署

**步骤：**

1. 在 GitHub 仓库中启用 GitHub Pages
2. 创建 GitHub Actions 工作流：

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build
      run: npm run build
      
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./out
```

### 4. 本地静态文件部署

如果你想生成静态文件手动部署到任何服务器：

```bash
# 构建静态文件
npm run build

# 生成的文件在 out 文件夹中
# 将 out 文件夹的内容上传到任何静态文件服务器
```

## 分享链接

部署完成后，你可以通过以下方式分享：

1. **直接分享链接**：将部署后的网站链接发送给朋友
2. **二维码分享**：使用在线二维码生成器创建网站二维码
3. **社交媒体分享**：网站内置了分享功能，支持原生分享 API

## 自定义配置

### 修改角色数据

编辑 `src/data/characters.ts` 文件来：
- 添加新角色
- 修改现有角色的属性和评分
- 调整排序选项

### 修改主题颜色

编辑 `tailwind.config.js` 文件中的颜色配置：

```javascript
colors: {
  'ww-primary': '#1a1a2e',    // 主色调
  'ww-gold': '#ffd700',       // 金色强调
  // ... 其他颜色
}
```

### 添加新的排序维度

在 `src/data/characters.ts` 中的 `sortOptions` 数组中添加新选项。

## 性能优化

网站已经进行了以下优化：
- 静态生成，加载速度快
- 响应式设计，适配所有设备
- 优化的图片和字体加载
- 压缩的 CSS 和 JavaScript

## 域名建议

推荐的域名格式：
- `wuthering-waves-tier.com`
- `mingchao-tier-list.com`
- `ww-character-ranking.com`

## 技术支持

如果遇到部署问题：
1. 检查 Node.js 版本（推荐 18+）
2. 确保所有依赖都已安装
3. 查看构建日志中的错误信息
4. 确认 `next.config.js` 中的 `output: 'export'` 配置正确
