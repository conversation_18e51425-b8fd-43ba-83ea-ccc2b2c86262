@tailwind base;
@tailwind components;
@tailwind utilities;

/* 确保基础样式加载 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #e2e8f0;
  font-family: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card {
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 215, 0, 0.5);
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: #ffd700;
  color: #1a1a2e;
}

.btn-primary:hover {
  background: #ffc107;
}

.btn-secondary {
  background: #16213e;
  color: #e2e8f0;
}

.btn-secondary:hover {
  background: #0f3460;
  color: #ffd700;
}

.progress-bar {
  width: 100%;
  background: #334155;
  border-radius: 9999px;
  height: 0.5rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.3s ease;
}

.tier-s-plus {
  color: #f87171;
  background: rgba(239, 68, 68, 0.2);
}

.tier-s {
  color: #fb923c;
  background: rgba(249, 115, 22, 0.2);
}

.tier-a {
  color: #fbbf24;
  background: rgba(245, 158, 11, 0.2);
}

.tier-b-plus {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.2);
}

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');

:root {
  --foreground-rgb: 226, 232, 240;
  --background-start-rgb: 26, 26, 46;
  --background-end-rgb: 15, 52, 96;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 响应式字体 */
@media (max-width: 640px) {
  .font-chinese {
    font-size: 0.9em;
  }
}

/* 选择文本颜色 */
::selection {
  background-color: #ffd700;
  color: #1a1a2e;
}
