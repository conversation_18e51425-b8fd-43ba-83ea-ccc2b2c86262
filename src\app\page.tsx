'use client';

import { useState, useMemo } from 'react';
import { characters, sortOptions } from '@/data/characters';
import { Character, SortOption } from '@/types/character';

export default function Home() {
  const [currentSort, setCurrentSort] = useState<SortOption>(sortOptions[0]);

  const sortedCharacters = useMemo(() => {
    const sorted = [...characters].sort((a, b) => {
      const aValue = a.stats[currentSort.key as keyof typeof a.stats] || a[currentSort.key as keyof Character];
      const bValue = b.stats[currentSort.key as keyof typeof b.stats] || b[currentSort.key as keyof Character];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return currentSort.order === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return currentSort.order === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }

      return 0;
    });

    return sorted;
  }, [currentSort]);

  // 内联样式定义
  const styles = {
    container: {
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      color: '#e2e8f0',
      fontFamily: "'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif",
      padding: '2rem 1rem'
    },
    header: {
      textAlign: 'center' as const,
      marginBottom: '2rem'
    },
    title: {
      fontSize: '3rem',
      fontWeight: 'bold',
      color: '#e2e8f0',
      marginBottom: '1rem'
    },
    subtitle: {
      fontSize: '1.125rem',
      color: '#94a3b8',
      marginBottom: '1.5rem'
    },
    card: {
      background: '#1e293b',
      border: '1px solid #334155',
      borderRadius: '0.5rem',
      padding: '1.5rem',
      marginBottom: '1.5rem',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
      transition: 'all 0.3s ease'
    },
    button: {
      padding: '0.75rem 1.5rem',
      borderRadius: '0.5rem',
      fontWeight: '600',
      border: 'none',
      cursor: 'pointer',
      margin: '0.25rem',
      transition: 'all 0.2s ease'
    },
    primaryButton: {
      background: '#ffd700',
      color: '#1a1a2e'
    },
    secondaryButton: {
      background: '#16213e',
      color: '#e2e8f0'
    },
    progressBar: {
      width: '100%',
      background: '#334155',
      borderRadius: '9999px',
      height: '0.5rem',
      overflow: 'hidden' as const,
      marginBottom: '0.5rem'
    },
    progressFill: {
      height: '100%',
      borderRadius: '9999px',
      transition: 'width 0.3s ease'
    }
  };

  const handleShare = async () => {
    const url = window.location.href;
    if (navigator.share) {
      try {
        await navigator.share({
          title: '鸣潮角色强度排行榜',
          text: '查看最新的鸣潮角色强度排行榜，了解各角色的详细数据分析！',
          url: url,
        });
      } catch (error) {
        console.log('分享失败:', error);
        copyToClipboard(url);
      }
    } else {
      copyToClipboard(url);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('链接已复制到剪贴板！');
    }).catch(() => {
      alert('复制失败，请手动复制链接');
    });
  };

  return (
    <div style={styles.container}>
      <div style={{maxWidth: '1200px', margin: '0 auto'}}>
        {/* 头部 */}
        <header style={styles.header}>
          <h1 style={styles.title}>
            鸣潮角色强度排行榜
          </h1>
          <p style={styles.subtitle}>
            基于当前版本的角色强度分析与排名
          </p>
          <div style={{display: 'flex', justifyContent: 'center', gap: '1rem', flexWrap: 'wrap'}}>
            <button
              onClick={handleShare}
              style={{...styles.button, ...styles.primaryButton}}
            >
              📤 分享排行榜
            </button>
            <div style={{...styles.card, padding: '0.75rem 1.5rem', margin: 0}}>
              <span style={{color: '#94a3b8'}}>
                更新时间: {new Date().toLocaleDateString('zh-CN')}
              </span>
            </div>
          </div>
        </header>

        {/* 排序控制 */}
        <div style={styles.card}>
          <h3 style={{fontSize: '1.125rem', fontWeight: 'bold', marginBottom: '1rem'}}>排序方式</h3>
          <div style={{display: 'flex', flexWrap: 'wrap', gap: '0.5rem'}}>
            {sortOptions.map((option) => (
              <button
                key={option.key}
                onClick={() => setCurrentSort(option)}
                style={{
                  ...styles.button,
                  ...(currentSort.key === option.key ? styles.primaryButton : styles.secondaryButton)
                }}
              >
                {option.label}
                {currentSort.key === option.key && (
                  <span style={{marginLeft: '0.5rem'}}>
                    {option.order === 'desc' ? '↓' : '↑'}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '2rem'}}>
          <div style={{...styles.card, textAlign: 'center'}}>
            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#ffd700'}}>{characters.length}</div>
            <div style={{color: '#94a3b8'}}>总角色数</div>
          </div>
          <div style={{...styles.card, textAlign: 'center'}}>
            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#f87171'}}>
              {characters.filter(c => c.tier === 'S+').length}
            </div>
            <div style={{color: '#94a3b8'}}>S+级角色</div>
          </div>
          <div style={{...styles.card, textAlign: 'center'}}>
            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#ffd700'}}>
              {characters.filter(c => c.rarity === 5).length}
            </div>
            <div style={{color: '#94a3b8'}}>五星角色</div>
          </div>
          <div style={{...styles.card, textAlign: 'center'}}>
            <div style={{fontSize: '2rem', fontWeight: 'bold', color: '#60a5fa'}}>
              {Math.round(characters.reduce((sum, c) => sum + c.stats.overall, 0) / characters.length)}
            </div>
            <div style={{color: '#94a3b8'}}>平均评分</div>
          </div>
        </div>

        {/* 角色列表 */}
        <div style={{display: 'flex', flexDirection: 'column', gap: '1.5rem'}}>
          {sortedCharacters.map((character, index) => {
            const getRankDisplay = (rank: number) => {
              if (rank === 1) return '🥇';
              if (rank === 2) return '🥈';
              if (rank === 3) return '🥉';
              return `#${rank}`;
            };

            const elementColors: {[key: string]: string} = {
              Anemo: '#4ecdc4',
              Glacio: '#87ceeb',
              Fusion: '#ff6b6b',
              Electro: '#9b59b6',
              Havoc: '#2c3e50',
              Spectro: '#f39c12',
            };

            const tierColors: {[key: string]: {color: string, background: string}} = {
              'S+': {color: '#f87171', background: 'rgba(239, 68, 68, 0.2)'},
              'S': {color: '#fb923c', background: 'rgba(249, 115, 22, 0.2)'},
              'A+': {color: '#fbbf24', background: 'rgba(245, 158, 11, 0.2)'},
              'A': {color: '#4ade80', background: 'rgba(34, 197, 94, 0.2)'},
              'B+': {color: '#60a5fa', background: 'rgba(59, 130, 246, 0.2)'},
              'B': {color: '#a78bfa', background: 'rgba(139, 92, 246, 0.2)'},
              'C': {color: '#9ca3af', background: 'rgba(107, 114, 128, 0.2)'},
            };

            return (
              <div key={character.id} style={styles.card}>
                {/* 头部信息 */}
                <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem'}}>
                  <div style={{display: 'flex', alignItems: 'center', gap: '1rem'}}>
                    <div style={{fontSize: '2rem', fontWeight: 'bold'}}>
                      {getRankDisplay(index + 1)}
                    </div>
                    <div>
                      <h3 style={{fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '0.5rem'}}>{character.name}</h3>
                      <div style={{display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem'}}>
                        <span style={{color: elementColors[character.element], fontWeight: '500'}}>
                          {character.element}
                        </span>
                        <span style={{color: '#94a3b8'}}>•</span>
                        <span style={{color: '#94a3b8'}}>{character.weapon}</span>
                        <span style={{color: '#94a3b8'}}>•</span>
                        <div style={{display: 'flex'}}>
                          {Array.from({ length: character.rarity }, (_, i) => (
                            <span key={i} style={{color: '#ffd700'}}>★</span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style={{
                    padding: '0.5rem 1rem',
                    borderRadius: '9999px',
                    fontSize: '0.875rem',
                    fontWeight: 'bold',
                    color: tierColors[character.tier].color,
                    background: tierColors[character.tier].background
                  }}>
                    {character.tier}
                  </div>
                </div>

                {/* 描述 */}
                <p style={{color: '#94a3b8', fontSize: '0.875rem', marginBottom: '1rem', lineHeight: '1.5'}}>
                  {character.description}
                </p>

                {/* 属性条 */}
                <div style={{marginBottom: '1rem'}}>
                  {[
                    {label: '综合评分', value: character.stats.overall, color: '#ffd700'},
                    {label: 'DPS输出', value: character.stats.dps, color: '#ef4444'},
                    {label: '生存能力', value: character.stats.survival, color: '#22c55e'},
                    {label: '辅助能力', value: character.stats.support, color: '#3b82f6'},
                    {label: '实用性', value: character.stats.utility, color: '#a855f7'}
                  ].map((stat) => (
                    <div key={stat.label} style={{marginBottom: '0.5rem'}}>
                      <div style={{display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem', marginBottom: '0.25rem'}}>
                        <span style={{color: '#94a3b8'}}>{stat.label}</span>
                        <span style={{fontWeight: '500'}}>{stat.value}</span>
                      </div>
                      <div style={styles.progressBar}>
                        <div
                          style={{
                            ...styles.progressFill,
                            width: `${stat.value}%`,
                            background: stat.color
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                {/* 优缺点 */}
                <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', fontSize: '0.875rem'}}>
                  <div>
                    <h4 style={{color: '#4ade80', fontWeight: '500', marginBottom: '0.5rem'}}>优势</h4>
                    <ul style={{listStyle: 'none', padding: 0}}>
                      {character.strengths.map((strength, i) => (
                        <li key={i} style={{color: '#94a3b8', marginBottom: '0.25rem'}}>
                          • {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 style={{color: '#f87171', fontWeight: '500', marginBottom: '0.5rem'}}>劣势</h4>
                    <ul style={{listStyle: 'none', padding: 0}}>
                      {character.weaknesses.map((weakness, i) => (
                        <li key={i} style={{color: '#94a3b8', marginBottom: '0.25rem'}}>
                          • {weakness}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 页脚 */}
        <footer style={{marginTop: '3rem', textAlign: 'center', color: '#94a3b8'}}>
          <p style={{marginBottom: '0.5rem'}}>
            数据仅供参考，实际游戏体验可能因个人操作和队伍配置而异
          </p>
          <p style={{fontSize: '0.875rem'}}>
            © 2024 鸣潮角色强度排行榜 - 非官方数据分析
          </p>
        </footer>
      </div>
    </div>
  );
}
