import { Character } from '@/types/character';

export const characters: Character[] = [
  {
    id: 'jinxi',
    name: '今汐',
    element: 'Spectro',
    weapon: 'Sword',
    rarity: 5,
    stats: {
      dps: 95,
      survival: 75,
      support: 85,
      utility: 90,
      overall: 91
    },
    tier: 'S+',
    description: '光谱属性主C，拥有极强的输出能力和优秀的机动性',
    strengths: ['超高单体输出', '优秀的机动性', '强力的共鸣技能'],
    weaknesses: ['对操作要求较高', '需要特定队伍配置']
  },
  {
    id: 'changli',
    name: '长离',
    element: 'Fusion',
    weapon: 'Sword',
    rarity: 5,
    stats: {
      dps: 92,
      survival: 70,
      support: 60,
      utility: 85,
      overall: 88
    },
    tier: 'S+',
    description: '热熔属性主C，拥有强大的爆发输出和范围伤害',
    strengths: ['极高爆发伤害', '优秀的范围清怪', '华丽的技能特效'],
    weaknesses: ['技能CD较长', '对能量管理要求高']
  },
  {
    id: 'chixia',
    name: '炽霞',
    element: 'Fusion',
    weapon: 'Pistols',
    rarity: 4,
    stats: {
      dps: 78,
      survival: 65,
      support: 45,
      utility: 70,
      overall: 72
    },
    tier: 'A',
    description: '热熔属性输出角色，适合新手使用的强力四星',
    strengths: ['操作简单', '输出稳定', '获取容易'],
    weaknesses: ['天花板较低', '后期乏力']
  },
  {
    id: 'kakaluo',
    name: '卡卡罗',
    element: 'Electro',
    weapon: 'Broadblade',
    rarity: 5,
    stats: {
      dps: 88,
      survival: 85,
      support: 70,
      utility: 82,
      overall: 85
    },
    tier: 'S',
    description: '雷属性主C，平衡的攻防能力和稳定的输出',
    strengths: ['攻防平衡', '稳定输出', '适应性强'],
    weaknesses: ['缺乏爆发性', '特色不够突出']
  },
  {
    id: 'yinlin',
    name: '吟霖',
    element: 'Electro',
    weapon: 'Rectifier',
    rarity: 5,
    stats: {
      dps: 90,
      survival: 60,
      support: 75,
      utility: 88,
      overall: 86
    },
    tier: 'S',
    description: '雷属性输出角色，拥有独特的连携机制',
    strengths: ['独特连携机制', '高机动性', '优秀的单体输出'],
    weaknesses: ['生存能力较弱', '需要熟练操作']
  },
  {
    id: 'weilinnai',
    name: '维里奈',
    element: 'Spectro',
    weapon: 'Rectifier',
    rarity: 5,
    stats: {
      dps: 70,
      survival: 90,
      support: 95,
      utility: 92,
      overall: 89
    },
    tier: 'S+',
    description: '光谱属性辅助角色，顶级的治疗和增益能力',
    strengths: ['顶级治疗能力', '强力团队增益', '优秀的生存保障'],
    weaknesses: ['个人输出较低', '依赖队友配合']
  },
  {
    id: 'baizhi',
    name: '白芷',
    element: 'Glacio',
    weapon: 'Rectifier',
    rarity: 4,
    stats: {
      dps: 45,
      survival: 85,
      support: 88,
      utility: 75,
      overall: 73
    },
    tier: 'A',
    description: '冰属性治疗角色，稳定的团队支援能力',
    strengths: ['稳定治疗', '冰属性共鸣', '获取容易'],
    weaknesses: ['输出能力极低', '技能单一']
  },
  {
    id: 'yangyang',
    name: '秧秧',
    element: 'Anemo',
    weapon: 'Sword',
    rarity: 4,
    stats: {
      dps: 65,
      survival: 70,
      support: 60,
      utility: 68,
      overall: 66
    },
    tier: 'B+',
    description: '风属性辅助角色，提供团队增益和控制',
    strengths: ['风属性聚怪', '团队增益', '操作简单'],
    weaknesses: ['输出偏低', '后期作用有限']
  },
  {
    id: 'sanhua',
    name: '散华',
    element: 'Glacio',
    weapon: 'Sword',
    rarity: 4,
    stats: {
      dps: 75,
      survival: 65,
      support: 55,
      utility: 70,
      overall: 69
    },
    tier: 'B+',
    description: '冰属性副C角色，提供冰属性伤害和控制',
    strengths: ['冰属性输出', '控制能力', '配队灵活'],
    weaknesses: ['天花板较低', '需要特定配队']
  }
];

export const sortOptions = [
  { key: 'overall' as const, label: '综合评分', order: 'desc' as const },
  { key: 'dps' as const, label: 'DPS输出', order: 'desc' as const },
  { key: 'survival' as const, label: '生存能力', order: 'desc' as const },
  { key: 'support' as const, label: '辅助能力', order: 'desc' as const },
  { key: 'utility' as const, label: '实用性', order: 'desc' as const },
  { key: 'name' as const, label: '角色名称', order: 'asc' as const },
];
